const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');

// Configure AWS SDK for SAM local
const awsConfig = {
    region: process.env.AWS_REGION || 'us-east-1'
};

const dynamodb = new AWS.DynamoDB.DocumentClient(awsConfig);
const s3 = new AWS.S3(awsConfig);

const USERS_TABLE = process.env.USERS_TABLE;
const USER_PROFILES_TABLE = process.env.USER_PROFILES_TABLE;
const FOLLOWS_TABLE = process.env.FOLLOWS_TABLE;
const AVATARS_BUCKET = process.env.AVATARS_BUCKET;

// Helper function to create response
const createResponse = (statusCode, body) => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Helper function to get user ID from authorizer context
const getUserIdFromContext = (event) => {
    // When using Lambda authorizer, user info is available in the context
    if (event.requestContext && event.requestContext.authorizer) {
        return event.requestContext.authorizer.userId;
    }
    return null;
};

// Get user profile
const getProfile = async (event) => {
    try {
        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get user from Users table
        const userResult = await dynamodb.get({
            TableName: USERS_TABLE,
            Key: { id: userId }
        }).promise();

        if (!userResult.Item) {
            return createResponse(404, { error: 'User not found' });
        }

        // Get user profile from UserProfiles table
        const profileResult = await dynamodb.get({
            TableName: USER_PROFILES_TABLE,
            Key: { user_id: userId }
        }).promise();

        const user = userResult.Item;
        const profile = profileResult.Item || {};

        return createResponse(200, {
            user: {
                id: user.id,
                email: user.email,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName,
                bio: profile.bio || '',
                avatarUrl: profile.avatarUrl || null,
                location: profile.location || '',
                website: profile.website || '',
                followersCount: profile.followersCount || 0,
                followingCount: profile.followingCount || 0,
                postsCount: profile.postsCount || 0,
                created_at: user.created_at,
                updated_at: user.updated_at
            }
        });

    } catch (error) {
        console.error('GetProfile error:', error);
        return createResponse(500, { error: 'Failed to get profile', details: error.message });
    }
};

// Update user profile
const updateProfile = async (event) => {
    try {
        const { firstName, lastName, bio, location, website, avatarUrl } = JSON.parse(event.body);

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Update Users table
        const userUpdateExpression = [];
        const userExpressionAttributeValues = {};

        if (firstName !== undefined) {
            userUpdateExpression.push('firstName = :firstName');
            userExpressionAttributeValues[':firstName'] = firstName;
        }

        if (lastName !== undefined) {
            userUpdateExpression.push('lastName = :lastName');
            userExpressionAttributeValues[':lastName'] = lastName;
        }

        userUpdateExpression.push('updated_at = :updated_at');
        userExpressionAttributeValues[':updated_at'] = new Date().toISOString();

        if (userUpdateExpression.length > 1) { // More than just updated_at
            await dynamodb.update({
                TableName: USERS_TABLE,
                Key: { id: userId },
                UpdateExpression: `SET ${userUpdateExpression.join(', ')}`,
                ExpressionAttributeValues: userExpressionAttributeValues
            }).promise();
        }

        // Update or create UserProfiles table entry
        const profileUpdateExpression = [];
        const profileExpressionAttributeValues = {};

        if (bio !== undefined) {
            profileUpdateExpression.push('bio = :bio');
            profileExpressionAttributeValues[':bio'] = bio;
        }

        if (location !== undefined) {
            profileUpdateExpression.push('#location = :location');
            profileExpressionAttributeValues[':location'] = location;
        }

        if (website !== undefined) {
            profileUpdateExpression.push('website = :website');
            profileExpressionAttributeValues[':website'] = website;
        }

        if (avatarUrl !== undefined) {
            profileUpdateExpression.push('avatarUrl = :avatarUrl');
            profileExpressionAttributeValues[':avatarUrl'] = avatarUrl;
        }

        profileUpdateExpression.push('updated_at = :updated_at');
        profileExpressionAttributeValues[':updated_at'] = new Date().toISOString();

        if (profileUpdateExpression.length > 0) {
            await dynamodb.update({
                TableName: USER_PROFILES_TABLE,
                Key: { user_id: userId },
                UpdateExpression: `SET ${profileUpdateExpression.join(', ')}`,
                ExpressionAttributeNames: location !== undefined ? { '#location': 'location' } : undefined,
                ExpressionAttributeValues: profileExpressionAttributeValues
            }).promise();
        }

        return createResponse(200, { message: 'Profile updated successfully' });

    } catch (error) {
        console.error('UpdateProfile error:', error);
        return createResponse(500, { error: 'Failed to update profile', details: error.message });
    }
};

// Get user by ID
const getUser = async (event) => {
    try {
        const { id } = event.pathParameters;

        const userResult = await dynamodb.get({
            TableName: USERS_TABLE,
            Key: { id }
        }).promise();

        if (!userResult.Item) {
            return createResponse(404, { error: 'User not found' });
        }

        const profileResult = await dynamodb.get({
            TableName: USER_PROFILES_TABLE,
            Key: { user_id: id }
        }).promise();

        const user = userResult.Item;
        const profile = profileResult.Item || {};

        return createResponse(200, {
            user: {
                id: user.id,
                username: user.username,
                firstName: user.firstName,
                lastName: user.lastName,
                bio: profile.bio || '',
                avatarUrl: profile.avatarUrl || null,
                location: profile.location || '',
                website: profile.website || '',
                followersCount: profile.followersCount || 0,
                followingCount: profile.followingCount || 0,
                postsCount: profile.postsCount || 0,
                created_at: user.created_at
            }
        });

    } catch (error) {
        console.error('GetUser error:', error);
        return createResponse(500, { error: 'Failed to get user', details: error.message });
    }
};

// Follow user
const followUser = async (event) => {
    try {
        const { id } = event.pathParameters; // user to follow

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        if (userId === id) {
            return createResponse(400, { error: 'Cannot follow yourself' });
        }

        // Check if already following
        const existingFollow = await dynamodb.get({
            TableName: FOLLOWS_TABLE,
            Key: { follower_id: userId, following_id: id }
        }).promise();

        if (existingFollow.Item) {
            return createResponse(400, { error: 'Already following this user' });
        }

        // Add follow relationship
        await dynamodb.put({
            TableName: FOLLOWS_TABLE,
            Item: {
                follower_id: userId,
                following_id: id,
                created_at: new Date().toISOString()
            }
        }).promise();

        // Update follower's following count
        await dynamodb.update({
            TableName: USER_PROFILES_TABLE,
            Key: { user_id: userId },
            UpdateExpression: 'ADD followingCount :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        }).promise();

        // Update followed user's followers count
        await dynamodb.update({
            TableName: USER_PROFILES_TABLE,
            Key: { user_id: id },
            UpdateExpression: 'ADD followersCount :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        }).promise();

        return createResponse(200, { message: 'User followed successfully' });

    } catch (error) {
        console.error('FollowUser error:', error);
        return createResponse(500, { error: 'Failed to follow user', details: error.message });
    }
};

// Unfollow user
const unfollowUser = async (event) => {
    try {
        const { id } = event.pathParameters; // user to unfollow

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Remove follow relationship
        await dynamodb.delete({
            TableName: FOLLOWS_TABLE,
            Key: { follower_id: userId, following_id: id }
        }).promise();

        // Update follower's following count
        await dynamodb.update({
            TableName: USER_PROFILES_TABLE,
            Key: { user_id: userId },
            UpdateExpression: 'ADD followingCount :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        }).promise();

        // Update followed user's followers count
        await dynamodb.update({
            TableName: USER_PROFILES_TABLE,
            Key: { user_id: id },
            UpdateExpression: 'ADD followersCount :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        }).promise();

        return createResponse(200, { message: 'User unfollowed successfully' });

    } catch (error) {
        console.error('UnfollowUser error:', error);
        return createResponse(500, { error: 'Failed to unfollow user', details: error.message });
    }
};

// Main handler
exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, pathParameters } = event;

    try {
        if (httpMethod === 'GET' && path === '/users/profile') {
            return await getProfile(event);
        } else if (httpMethod === 'PUT' && path === '/users/profile') {
            return await updateProfile(event);
        } else if (httpMethod === 'GET' && pathParameters && pathParameters.id) {
            return await getUser(event);
        } else if (httpMethod === 'POST' && path.includes('/follow')) {
            return await followUser(event);
        } else if (httpMethod === 'DELETE' && path.includes('/follow')) {
            return await unfollowUser(event);
        } else {
            return createResponse(404, { error: 'Not found' });
        }
    } catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: error.message });
    }
};
